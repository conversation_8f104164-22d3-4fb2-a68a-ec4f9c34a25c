import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Menu } from 'lucide-react';
import PropTypes from 'prop-types';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const HamburgerMenu = React.memo(({ isOpen, onClick, className = '' }) => {
  const menuRef = useRef();

  useEffect(() => {
    const menuElement = menuRef.current;
    if (!menuElement) return;

    // Check for reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (prefersReducedMotion) {
      // Set final state without animation
      gsap.set(menuElement, { opacity: 1, scale: 1 });
      return;
    }

    // Initial setup - ensure it's always visible
    gsap.set(menuElement, {
      scale: 1,
      opacity: 1,
      y: 0
    });

    // Scroll behavior - follow scroll with subtle parallax
    ScrollTrigger.create({
      start: 0,
      end: 99999,
      scrub: 0.3,
      onUpdate: (self) => {
        const scrollY = window.scrollY;
        const velocity = self.getVelocity();

        // Calculate follow offset (menu moves slower than scroll)
        const followY = scrollY * 0.005; // 0.5% of scroll distance
        const maxFollow = 5; // Maximum follow distance
        const clampedFollow = Math.min(followY, maxFollow);

        // Apply subtle parallax effect to keep it visible
        gsap.set(menuElement, {
          y: clampedFollow
        });

        // Compact slightly when scrolling fast
        if (Math.abs(velocity) > 500) {
          gsap.to(menuElement, {
            scale: 0.95,
            duration: 0.2,
            ease: "power2.out"
          });
        } else {
          gsap.to(menuElement, {
            scale: 1,
            duration: 0.3,
            ease: "power2.out"
          });
        }
      }
    });

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(st => {
        if (st.trigger === menuElement) {
          st.kill();
        }
      });
    };
  }, []);

  return (
    <motion.button
      ref={menuRef}
      onClick={onClick}
      className={`
        hamburger-menu fixed top-4 left-4 z-50 p-3 rounded-lg
        bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700
        text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100
        hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200
        ${className}
      `}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label={isOpen ? 'Đóng menu' : 'Mở menu'}
    >
      <motion.div
        animate={{ rotate: isOpen ? 90 : 0 }}
        transition={{ duration: 0.2 }}
      >
        <Menu size={20} />
      </motion.div>
    </motion.button>
  );
});

HamburgerMenu.displayName = 'HamburgerMenu';

HamburgerMenu.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  className: PropTypes.string
};

export default HamburgerMenu;
